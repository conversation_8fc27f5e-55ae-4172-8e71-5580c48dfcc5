package main

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/Unleash/unleash-client-go/v4"
	unleashContext "github.com/Unleash/unleash-client-go/v4/context"
	"github.com/google/uuid"
)

type Config struct {
	ExperimentName     string
	ExperimentGroupA   string
	ExperimentGroupB   string
	ExperimentEventHit string
	SoguURL            string
	ProxyURL           string
	RedirectBaseURL    string
	RedirectTarget     *url.URL
	UnleashURL         string
	UnleashAPIKey      string
	UnleashAppName     string
	UnleashFeature     string
	ServiceTarget      *url.URL
	AppVersion         string
	Port               string
	ShutdownTimeout    time.Duration
	HTTPTimeout        time.Duration
}

type App struct {
	config        *Config
	httpClient    *http.Client
	logger        *slog.Logger
	server        *http.Server
	unleashClient *unleash.Client
}

func main() {
	// Initialize structured logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Load configuration
	config, err := loadConfig()
	if err != nil {
		logger.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	// Initialize Unleash client
	unleashClient, err := unleash.NewClient(
		unleash.WithUrl(config.UnleashURL),
		unleash.WithInstanceId(config.UnleashAPIKey),
		unleash.WithAppName(config.UnleashAppName),
		unleash.WithRefreshInterval(15*time.Second),
	)
	if err != nil {
		logger.Error("Failed to initialize Unleash client", "error", err)
		os.Exit(1)
	}

	// Create application instance
	app := &App{
		config: config,
		httpClient: &http.Client{
			Timeout: config.HTTPTimeout,
		},
		logger:        logger,
		unleashClient: unleashClient,
	}

	// Setup HTTP server with timeouts
	mux := http.NewServeMux()
	mux.HandleFunc("/", app.splitHandler)
	mux.HandleFunc("/health", app.healthHandler)

	app.server = &http.Server{
		Addr:         ":" + config.Port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Log configuration
	logger.Info("Configuration loaded",
		"proxy_url", config.ProxyURL,
		"redirect_base_url", config.RedirectBaseURL,
		"unleash_feature", config.UnleashFeature)

	// Start server in goroutine
	go func() {
		logger.Info("Starting splitter server", "port", config.Port, "version", app.config.AppVersion)
		if err := app.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Error("Server failed to start", "error", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal for graceful shutdown
	app.gracefulShutdown()
}

// loadConfig loads configuration from environment variables
func loadConfig() (*Config, error) {
	config := &Config{
		ExperimentName:     getenv("EXPERIMENT_NAME", "monolit-mf-ab-test"),
		ExperimentGroupA:   getenv("EXPERIMENT_GROUP_A", "Monolit"),
		ExperimentGroupB:   getenv("EXPERIMENT_GROUP_B", "MF"),
		ExperimentEventHit: getenv("EXPERIMENT_EVENT_HIT", "experiments.hit"),
		RedirectBaseURL:    getenv("REDIRECT_BASE_URL", "https://google.com"),
		ProxyURL:           getenv("PROXY_URL", "https://ya.ru"),
		SoguURL:            getenv("SOGU_URL", "https://sogu-staging.sogu.dev.tripster.tech/events/"),
		UnleashURL:         getenv("UNLEASH_URL", ""),
		UnleashAPIKey:      getenv("UNLEASH_API_KEY", ""),
		UnleashAppName:     getenv("UNLEASH_APP_NAME", "splitter"),
		UnleashFeature:     getenv("UNLEASH_FEATURE", "monolit-mf-ab-test"),
		Port:               getenv("PORT", "8080"),
		AppVersion:         getenv("APP_VERSION", "staging"),
		ShutdownTimeout:    30 * time.Second,
		HTTPTimeout:        10 * time.Second,
	}

	// Parse service target URL
	serviceTarget, err := url.Parse(config.ProxyURL)
	if err != nil {
		return nil, fmt.Errorf("invalid PROXY_URL: %v", err)
	}
	config.ServiceTarget = serviceTarget

	// Parse redirect target URL
	redirectTarget, err := url.Parse(config.RedirectBaseURL)
	if err != nil {
		return nil, fmt.Errorf("invalid REDIRECT_BASE_URL: %v", err)
	}
	config.RedirectTarget = redirectTarget

	return config, nil
}

// gracefulShutdown handles graceful shutdown of the application
func (app *App) gracefulShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	app.logger.Info("Shutting down server...")

	// Close Unleash client
	err := app.unleashClient.Close()
	if err != nil {
		app.logger.Error("Failed to close Unleash client", "error", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), app.config.ShutdownTimeout)
	defer cancel()

	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("Server forced to shutdown", "error", err)
		os.Exit(1)
	}

	app.logger.Info("Server exited")
}

// splitHandler handles the main A/B testing logic
func (app *App) splitHandler(w http.ResponseWriter, r *http.Request) {
	deviceID := app.getOrSetDeviceID(w, r)

	// Create Unleash context with user ID
	unleashCtx := &unleashContext.Context{
		UserId: deviceID,
	}

	// Check if feature is enabled for this user
	//enabled := app.unleashClient.IsEnabled(app.config.UnleashFeature, unleash.WithContext(*unleashCtx))
	variant := app.unleashClient.GetVariant(app.config.UnleashFeature, unleash.WithVariantContext(*unleashCtx))

	// Determine group based on Unleash decision
	var group string
	if variant.FeatureEnabled {
		group = app.config.ExperimentGroupB
	} else {
		group = app.config.ExperimentGroupA
	}

	// Redirect group B to different URL
	if group == app.config.ExperimentGroupB {
		redirectURL := *app.config.RedirectTarget
		redirectURL.Path += r.URL.Path
		redirectURL.RawQuery = r.URL.RawQuery

		// Send analytics for redirect (HTML page request)
		go app.sendAnalytics(r, deviceID, group)

		http.Redirect(w, r, redirectURL.String(), http.StatusFound)
		return
	}

	// Wrap response writer to capture Content-Type
	wrapper := &responseWriterWrapper{
		ResponseWriter: w,
		statusCode:     http.StatusOK,
	}

	// Proxy to the target service
	proxy := httputil.NewSingleHostReverseProxy(app.config.ServiceTarget)

	// Preserve original request path
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalPath := req.URL.Path
		originalDirector(req)
		req.URL.Path = originalPath

		// Set proper Host header
		req.Host = app.config.ServiceTarget.Host
	}

	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		app.logger.Error("Proxy error",
			"error", err,
			"path", r.URL.Path,
			"method", r.Method)
		http.Error(w, "Service temporarily unavailable", http.StatusBadGateway)
	}

	proxy.ServeHTTP(wrapper, r)

	// Send analytics after proxy response, based on Content-Type
	contentType := wrapper.Header().Get("Content-Type")
	if app.shouldSendAnalytics(r, contentType) {
		go app.sendAnalytics(r, deviceID, group)
	}
}

func (app *App) getCookie(r *http.Request, name string) string {
	cookie, err := r.Cookie(name)
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}
	return ""
}

func (app *App) getOrSetDeviceID(w http.ResponseWriter, r *http.Request) string {
	cookieValue := app.getCookie(r, "device_id")
	if cookieValue != "" {
		return cookieValue
	}

	newID := uuid.New().String()
	http.SetCookie(w, &http.Cookie{
		Name:     "device_id",
		Value:    newID,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   30 * 24 * 3600, // 30 days
	})
	return newID
}

// responseWriterWrapper wraps http.ResponseWriter to capture response headers
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode  int
	headersSent bool
}

func (w *responseWriterWrapper) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.headersSent = true
	w.ResponseWriter.WriteHeader(statusCode)
}

func (w *responseWriterWrapper) Write(data []byte) (int, error) {
	if !w.headersSent {
		w.WriteHeader(http.StatusOK)
	}
	return w.ResponseWriter.Write(data)
}

// shouldSendAnalytics determines if analytics should be sent based on Content-Type
func (app *App) shouldSendAnalytics(r *http.Request, contentType string) bool {
	// Only send analytics for GET requests
	if r.Method != "GET" {
		return false
	}

	// Send analytics only for HTML content
	return strings.Contains(strings.ToLower(contentType), "text/html")
}

func (app *App) sendAnalytics(r *http.Request, deviceID, group string) {
	if app.config.SoguURL == "" {
		return
	}

	gaClientID := app.getCookie(r, "_ga")
	yandexClientID := app.getCookie(r, "_ym_uid")

	type Params struct {
		DeviceID     string `json:"device_id"`
		Experiment   string `json:"experiment"`
		Variant      string `json:"variant"`
		WasGenerated bool   `json:"was_generated"`
	}

	type Event struct {
		AppVersion string `json:"app_version"`
		EventName  string `json:"event_name"`
		Platform   string `json:"platform"`
		URL        string `json:"url"`
		GAClientID string `json:"ga_client_id"`
		YAClientID string `json:"ya_client_id"`
		UserAgent  string `json:"user_agent"`
		DeviceID   string `json:"device_id"`
		DT         int64  `json:"dt"`
		Params     Params `json:"params"`
	}

	event := Event{
		AppVersion: app.config.AppVersion,
		EventName:  app.config.ExperimentEventHit,
		Platform:   "web",
		URL:        r.URL.String(),
		GAClientID: gaClientID,
		YAClientID: yandexClientID,
		UserAgent:  r.Header.Get("User-Agent"),
		DeviceID:   deviceID,
		DT:         time.Now().Unix(),
		Params: Params{
			DeviceID:     deviceID,
			Experiment:   app.config.ExperimentName,
			Variant:      group,
			WasGenerated: false,
		},
	}

	body, err := json.Marshal([]Event{event})
	if err != nil {
		app.logger.Error("Failed to marshal analytics payload", "error", err)
		return
	}

	app.logger.Debug("Sending analytics event", "device_id", deviceID, "group", group, "url", r.URL.Path)

	if app.config.AppVersion == "local" {
		return
	}

	resp, err := app.httpClient.Post(app.config.SoguURL, "application/json", bytes.NewBuffer(body))
	if err != nil {
		app.logger.Error("Failed to send analytics", "error", err)
		return
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			app.logger.Error("Failed to close response body", "error", err)
		}
	}(resp.Body)
}

// Health check handlers
func (app *App) healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"status":  "healthy",
		"version": app.config.AppVersion,
		"time":    time.Now().UTC().Format(time.RFC3339),
	}

	err := json.NewEncoder(w).Encode(response)
	if err != nil {
		app.logger.Error("Failed to encode health response", "error", err)
		return
	}
}

func getenv(key, fallback string) string {
	val := os.Getenv(key)
	if val == "" {
		return fallback
	}
	return val
}
